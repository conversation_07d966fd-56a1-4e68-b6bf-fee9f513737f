.VCU {
  background-color: #dfdcff;
  display: flex;
  flex-direction: row;
  justify-content: center;
  width: 100%;
}

.VCU .div {
  background-color: #dfdcff;
  overflow: hidden;
  width: 600px;
  height: 2537px;
  position: relative;
}

.VCU .frame {
  display: flex;
  flex-direction: column;
  width: 600px;
  align-items: flex-start;
  gap: 10px;
  padding: 16px 10px 16px 32px;
  position: absolute;
  top: 0;
  left: 0;
  background-color: #ffffff;
}

.VCU .lockup {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  position: relative;
  flex: 0 0 auto;
}

.VCU .lockup-producticon {
  position: relative;
  width: 32px;
  height: 32px;
}

.VCU .ico-icon {
  height: 32px;
  box-shadow: inset 0px 0px 0.12px #0000003d;
}

.VCU .mask {
  width: 32px;
  height: 32px;
  border-radius: 7.3px;
  background-image: url(https://c.animaapp.com/mcujcvmlUBXVNG/img/producticon.svg);
  background-size: 100% 100%;
}

.VCU .lockup-productname {
  display: inline-flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  gap: 4px;
  position: relative;
  flex: 0 0 auto;
}

.VCU .lockup-companyname {
  display: inline-flex;
  align-items: center;
  padding: 0px 0px 0px 1px;
  position: relative;
  flex: 0 0 auto;
}

.VCU .text-wrapper {
  position: relative;
  width: fit-content;
  margin-top: -1px;
  font-family: "Messina Sans-Regular", Helvetica;
  font-weight: 400;
  color: #000000;
  font-size: 12px;
  letter-spacing: -0.48px;
  line-height: 12px;
  white-space: nowrap;
}

.VCU .product-wrapper {
  display: inline-flex;
  align-items: center;
  position: relative;
  flex: 0 0 auto;
}

.VCU .product {
  position: relative;
  width: fit-content;
  margin-top: -1px;
  font-family: "Messina Sans-Bold", Helvetica;
  font-weight: 700;
  color: #000000;
  font-size: 20px;
  letter-spacing: -0.8px;
  line-height: normal;
  white-space: nowrap;
}

.VCU .view {
  display: flex;
  flex-direction: column;
  width: 600px;
  height: 311px;
  align-items: flex-start;
  gap: 24px;
  padding: 40px 32px 0px;
  position: absolute;
  top: 2226px;
  left: 0;
  background-color: var(--labellightprimary);
}

.VCU .brand {
  position: relative;
  align-self: stretch;
  width: 100%;
  height: 72px;
}

.VCU .main-vertical {
  position: absolute;
  width: 120px;
  height: 72px;
  top: 0;
  left: 0;
}

.VCU .socialapp-group {
  display: inline-flex;
  align-items: center;
  justify-content: flex-end;
  gap: 13px;
  position: absolute;
  top: 24px;
  left: 401px;
}

.VCU .social-app {
  position: relative;
  width: 24px;
  height: 24px;
}

.VCU .subtract {
  position: absolute;
  width: 20px;
  height: 15px;
  top: 4px;
  left: 2px;
}

.VCU .caption {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  position: relative;
  align-self: stretch;
  width: 100%;
  flex: 0 0 auto;
}

.VCU .you-are-receiving {
  position: relative;
  align-self: stretch;
  margin-top: -1px;
  font-family: var(--caption-font-family);
  font-weight: var(--caption-font-weight);
  color: var(--labeldarksecondary);
  font-size: var(--caption-font-size);
  letter-spacing: var(--caption-letter-spacing);
  line-height: var(--caption-line-height);
  font-style: var(--caption-font-style);
}

.VCU .span {
  line-height: var(--caption-line-height);
  font-family: var(--caption-font-family);
  font-style: var(--caption-font-style);
  font-weight: var(--caption-font-weight);
  letter-spacing: var(--caption-letter-spacing);
  font-size: var(--caption-font-size);
}

.VCU .text-wrapper-2 {
  line-height: var(--caption-line-height);
  text-decoration: underline;
  font-family: var(--caption-font-family);
  font-style: var(--caption-font-style);
  font-weight: var(--caption-font-weight);
  letter-spacing: var(--caption-letter-spacing);
  font-size: var(--caption-font-size);
}

.VCU .please-do-not-reply {
  position: relative;
  align-self: stretch;
  font-family: "Segoe UI-Regular", Helvetica;
  font-weight: 400;
  color: var(--labeldarksecondary);
  font-size: 12px;
  letter-spacing: 0;
  line-height: 12px;
}

.VCU .text-wrapper-3 {
  line-height: 16px;
}

.VCU .text-wrapper-4 {
  line-height: 16px;
  text-decoration: underline;
}

.VCU .about-wondershare {
  position: relative;
  width: fit-content;
  font-family: "Segoe UI-Regular", Helvetica;
  font-weight: 400;
  color: var(--labeldarksecondary);
  font-size: 12px;
  letter-spacing: 0;
  line-height: 12px;
}

.VCU .text-wrapper-5 {
  color: #999999;
  line-height: 16px;
  text-decoration: underline;
}

.VCU .text-wrapper-6 {
  color: #999999;
  line-height: 0.1px;
}

.VCU .p {
  position: relative;
  align-self: stretch;
  font-family: var(--caption-font-family);
  font-weight: var(--caption-font-weight);
  color: var(--labeldarksecondary);
  font-size: var(--caption-font-size);
  letter-spacing: var(--caption-letter-spacing);
  line-height: var(--caption-line-height);
  font-style: var(--caption-font-style);
}

.VCU .frame-2 {
  display: inline-flex;
  height: 56px;
  align-items: center;
  gap: 16px;
  position: absolute;
  top: 652px;
  left: 120px;
}

.VCU .filled-large-icon {
  background-color: #7859f6;
  display: flex;
  width: 172px;
  height: 56px;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 19px 16px;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

.VCU .text-wrapper-7 {
  position: relative;
  width: fit-content;
  margin-top: -6px;
  margin-bottom: -2px;
  font-family: "HarmonyOS Sans-Bold", Helvetica;
  font-weight: 700;
  color: #ffffff;
  font-size: 18px;
  text-align: center;
  letter-spacing: 0;
  line-height: 26px;
  white-space: nowrap;
}

.VCU .div-wrapper {
  border: 2px solid;
  border-color: #7859f6;
  display: flex;
  width: 172px;
  height: 56px;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 19px 16px;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

.VCU .text-wrapper-8 {
  position: relative;
  width: fit-content;
  margin-top: -6px;
  margin-bottom: -2px;
  font-family: "HarmonyOS Sans-Bold", Helvetica;
  font-weight: 700;
  color: #7859f6;
  font-size: 18px;
  text-align: center;
  letter-spacing: 0;
  line-height: 26px;
  white-space: nowrap;
}

.VCU .edit-boldly-create {
  position: absolute;
  width: 501px;
  top: 92px;
  left: 50px;
  font-family: "Outfit", Helvetica;
  font-weight: 700;
  color: #000000;
  font-size: 36px;
  text-align: center;
  letter-spacing: 0;
  line-height: 42px;
}

.VCU .filmora-your-easy {
  position: absolute;
  top: 184px;
  left: 93px;
  font-family: "Outfit", Helvetica;
  font-weight: 400;
  color: transparent;
  font-size: 18px;
  text-align: center;
  letter-spacing: 0;
  line-height: 18px;
}

.VCU .text-wrapper-9 {
  font-weight: 700;
  color: #770af6;
  line-height: 24px;
}

.VCU .text-wrapper-10 {
  color: #000000;
  line-height: 24px;
}

.VCU .group {
  position: absolute;
  width: 608px;
  height: 472px;
  top: 756px;
  left: 0;
}

.VCU .overlap {
  position: absolute;
  width: 600px;
  height: 112px;
  top: 0;
  left: 0;
}

.VCU .advanced-features {
  position: absolute;
  width: 348px;
  top: 40px;
  left: 129px;
  font-family: "Outfit", Helvetica;
  font-weight: 600;
  color: transparent;
  font-size: 28px;
  text-align: center;
  letter-spacing: 0;
  line-height: 32px;
}

.VCU .text-wrapper-11 {
  color: #000000;
}

.VCU .text-wrapper-12 {
  color: #770af6;
}

.VCU .overlap-group {
  position: absolute;
  width: 600px;
  height: 178px;
  top: 294px;
  left: 0;
}

.VCU .text-wrapper-13 {
  position: absolute;
  top: 10px;
  left: 47px;
  font-family: "HarmonyOS Sans-Bold", Helvetica;
  font-weight: 700;
  color: #41008c;
  font-size: 16px;
  letter-spacing: 0;
  line-height: 22px;
  white-space: nowrap;
}

.VCU .element-cloud-storage {
  position: absolute;
  top: 40px;
  left: 44px;
  font-family: "HarmonyOS Sans-Regular", Helvetica;
  font-weight: 400;
  color: #41008c;
  font-size: 12px;
  letter-spacing: 0;
  line-height: 24px;
}

.VCU .element-HD-export-mo {
  position: absolute;
  top: 40px;
  left: 321px;
  font-family: "HarmonyOS Sans-Regular", Helvetica;
  font-weight: 400;
  color: #41008c;
  font-size: 12px;
  letter-spacing: 0;
  line-height: 24px;
}

.VCU .group-wrapper {
  position: absolute;
  width: 600px;
  height: 182px;
  top: 112px;
  left: 0;
}

.VCU .overlap-group-wrapper {
  position: relative;
  width: 511px;
  height: 138px;
  top: 22px;
  left: 47px;
}

.VCU .overlap-group-2 {
  position: relative;
  width: 507px;
  height: 138px;
  background-image: url(https://c.animaapp.com/mcujcvmlUBXVNG/img/subtract-1.svg);
  background-size: 100% 100%;
}

.VCU .frame-wrapper {
  position: absolute;
  width: 170px;
  height: 138px;
  top: 0;
  left: 337px;
  background-image: url(https://c.animaapp.com/mcujcvmlUBXVNG/img/intersect.svg);
  background-size: 100% 100%;
}

.VCU .frame-3 {
  display: flex;
  flex-direction: column;
  width: 117px;
  align-items: center;
  gap: 10px;
  position: relative;
  top: 30px;
  left: 28px;
}

.VCU .text-wrapper-14 {
  position: relative;
  align-self: stretch;
  margin-top: -1px;
  font-family: "Outfit", Helvetica;
  font-weight: 700;
  color: #ffffff;
  font-size: 24px;
  text-align: center;
  letter-spacing: 0;
  line-height: 28px;
}

.VCU .img-wrapper {
  position: relative;
  width: 40px;
  height: 40px;
  background-color: #000000;
  border-radius: 20px;
}

.VCU .img {
  position: absolute;
  width: 16px;
  height: 14px;
  top: 13px;
  left: 12px;
}

.VCU .after-the-trial-auto {
  position: absolute;
  width: 285px;
  top: 66px;
  left: 25px;
  font-family: "HarmonyOS Sans-Regular", Helvetica;
  font-weight: 400;
  color: transparent;
  font-size: 14px;
  text-align: center;
  letter-spacing: 0;
  line-height: 22px;
}

.VCU .text-wrapper-15 {
  font-family: "HarmonyOS Sans-Bold", Helvetica;
  font-weight: 700;
  color: #770af6;
}

.VCU .text-wrapper-16 {
  position: absolute;
  top: 30px;
  left: 46px;
  background: linear-gradient(
    90deg,
    rgba(255, 114, 224, 1) 0%,
    rgba(144, 98, 255, 1) 55%,
    rgba(87, 144, 255, 1) 100%
  );
  -webkit-background-clip: text !important;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
  font-family: "Outfit", Helvetica;
  font-weight: 800;
  color: transparent;
  font-size: 30px;
  text-align: center;
  letter-spacing: 0;
  line-height: 28px;
  white-space: nowrap;
}

.VCU .frame-4 {
  position: absolute;
  width: 600px;
  height: 998px;
  top: 1228px;
  left: 0;
  background-color: #ffffff;
}

.VCU .enjoy-the-AI-video {
  position: absolute;
  width: 531px;
  top: 47px;
  left: 25px;
  font-family: "Outfit", Helvetica;
  font-weight: 700;
  color: transparent;
  font-size: 32px;
  text-align: center;
  letter-spacing: 0;
  line-height: 42px;
}

.VCU .frame-5 {
  display: inline-flex;
  align-items: flex-start;
  justify-content: center;
  gap: 16px;
  position: absolute;
  top: 877px;
  left: 104px;
}

.VCU .filled-large-icon-2 {
  display: flex;
  width: 188px;
  height: 56px;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 19px 16px;
  position: relative;
  background-color: #7859f6;
  border-radius: 8px;
  overflow: hidden;
}

.VCU .windows {
  position: relative;
  width: 24px;
  height: 24px;
  margin-top: -3px;
  margin-bottom: -3px;
}

.VCU .icon-windows {
  background-image: url(https://c.animaapp.com/mcujcvmlUBXVNG/img/platform-system-icon.svg);
  background-size: 100% 100%;
  position: relative;
  width: 24px;
  height: 24px;
  margin-top: -3px;
  margin-bottom: -3px;
}

.VCU .group-2 {
  position: absolute;
  width: 552px;
  height: 416px;
  top: 204px;
  left: 24px;
}

.VCU .frame-6 {
  position: absolute;
  width: 268px;
  height: 200px;
  top: 0;
  left: 0;
  background-color: #e0deff;
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid;
  border-color: #7859f680;
}

.VCU .frame-7 {
  position: absolute;
  width: 268px;
  height: 150px;
  top: 0;
  left: 0;
  background-color: #000000;
}

.VCU .img-2 {
  position: absolute;
  width: 268px;
  height: 150px;
  top: 0;
  left: 0;
  object-fit: cover;
}

.VCU .frame-8 {
  display: flex;
  flex-direction: column;
  width: 216px;
  align-items: center;
  gap: 12px;
  position: absolute;
  top: 166px;
  left: 26px;
}

.VCU .text-wrapper-17 {
  position: relative;
  align-self: stretch;
  margin-top: -1px;
  font-family: "Outfit", Helvetica;
  font-weight: 700;
  color: #000000;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 18px;
  text-decoration: underline;
}

.VCU .frame-9 {
  position: absolute;
  width: 268px;
  height: 200px;
  top: 216px;
  left: 0;
  background-color: #e0deff;
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid;
  border-color: #7859f680;
}

.VCU .frame-10 {
  position: absolute;
  width: 268px;
  height: 200px;
  top: 0;
  left: 284px;
  background-color: #e0deff;
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid;
  border-color: #7859f680;
}

.VCU .frame-11 {
  height: 150px;
  top: 0;
  left: 0;
  background-color: #000000;
  position: absolute;
  width: 268px;
  overflow: hidden;
}

.VCU .overlap-wrapper {
  position: relative;
  height: 286px;
  top: -68px;
  background-color: #000000;
  border-radius: 9.53px;
  overflow: hidden;
}

.VCU .overlap-2 {
  position: relative;
  width: 488px;
  height: 326px;
  top: -20px;
  left: -148px;
}

.VCU .group-3 {
  position: absolute;
  width: 488px;
  height: 326px;
  top: 0;
  left: 0;
}

.VCU .overlap-group-3 {
  position: relative;
  width: 268px;
  height: 218px;
  top: 88px;
  left: 148px;
}

.VCU .img-3 {
  position: absolute;
  width: 268px;
  height: 150px;
  top: 0;
  left: 0;
}

.VCU .rectangle {
  position: absolute;
  width: 268px;
  height: 119px;
  top: 99px;
  left: 0;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 1) 100%
  );
  opacity: 0.2;
}

.VCU .rectangle-2 {
  position: absolute;
  width: 268px;
  height: 119px;
  top: 187px;
  left: 148px;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 1) 100%
  );
  opacity: 0.5;
}

.VCU .group-4 {
  position: absolute;
  width: 147px;
  height: 50px;
  top: 196px;
  left: 208px;
}

.VCU .overlap-3 {
  position: relative;
  height: 42px;
}

.VCU .card {
  display: flex;
  width: 143px;
  height: 34px;
  align-items: center;
  gap: 10.64px;
  padding: 1.76px 3.52px 1.76px 1.76px;
  position: absolute;
  top: 4px;
  left: 4px;
  background-color: #21282d;
  border-radius: 4.8px;
  box-shadow: 0px 0px 4.71px #55e5c580;
}

.VCU .frame-12 {
  position: relative;
  width: 30.78px;
  height: 30.78px;
}

.VCU .music-normal {
  width: 31px;
  height: 31px;
  background-size: 100% 100%;
}

.VCU .overlap-4 {
  position: relative;
  height: 26px;
  top: 4px;
}

.VCU .ellipse {
  position: absolute;
  width: 5px;
  height: 6px;
  top: 14px;
  left: 11px;
  background-color: #34ffc299;
  border-radius: 2.64px / 3.08px;
  filter: blur(3.3px);
}

.VCU .ellipse-2 {
  position: absolute;
  width: 5px;
  height: 6px;
  top: 15px;
  left: 15px;
  background-color: #00473299;
  border-radius: 2.64px / 3.08px;
  filter: blur(3.3px);
}

.VCU .ellipse-3 {
  position: absolute;
  width: 12px;
  height: 15px;
  top: 8px;
  left: 11px;
}

.VCU .frame-13 {
  position: absolute;
  width: 23px;
  height: 24px;
  top: 0;
  left: 3px;
}

.VCU .overlap-group-4 {
  position: relative;
  width: 33px;
  height: 33px;
  top: -4px;
  left: -5px;
}

.VCU .ellipse-4 {
  position: absolute;
  width: 24px;
  height: 24px;
  top: 5px;
  left: 5px;
}

.VCU .ellipse-5 {
  position: absolute;
  width: 25px;
  height: 25px;
  top: 4px;
  left: 4px;
  border-radius: 12.65px;
  background: conic-gradient(
    from 270deg at 50% 50%,
    rgba(255, 255, 255, 0.04) 3%,
    rgba(255, 255, 255, 0) 5%,
    rgba(255, 255, 255, 0.58) 11%,
    rgba(255, 255, 255, 0.68) 19%,
    rgba(255, 255, 255, 0) 23%,
    rgba(255, 255, 255, 0) 55%,
    rgba(255, 255, 255, 1) 62%,
    rgba(255, 255, 255, 1) 64%,
    rgba(255, 255, 255, 1) 66%,
    rgba(255, 255, 255, 0) 73%
  );
  opacity: 0.12;
}

.VCU .ellipse-6 {
  position: absolute;
  width: 25px;
  height: 25px;
  top: 4px;
  left: 4px;
  border-radius: 12.65px;
  transform: rotate(23.7deg);
  background: conic-gradient(
    from 270deg at 50% 50%,
    rgba(255, 255, 255, 0.04) 3%,
    rgba(255, 255, 255, 0) 5%,
    rgba(255, 255, 255, 0.58) 11%,
    rgba(255, 255, 255, 0.68) 19%,
    rgba(255, 255, 255, 0) 23%,
    rgba(255, 255, 255, 0) 55%,
    rgba(255, 255, 255, 1) 62%,
    rgba(255, 255, 255, 1) 64%,
    rgba(255, 255, 255, 1) 66%,
    rgba(255, 255, 255, 0) 73%
  );
  opacity: 0.12;
}

.VCU .ellipse-7 {
  position: absolute;
  width: 25px;
  height: 25px;
  top: 4px;
  left: 4px;
  border-radius: 12.65px;
  transform: rotate(90deg);
  background: conic-gradient(
    from 270deg at 50% 50%,
    rgba(255, 255, 255, 0.04) 3%,
    rgba(255, 255, 255, 0) 5%,
    rgba(255, 255, 255, 0.58) 11%,
    rgba(255, 255, 255, 0.68) 19%,
    rgba(255, 255, 255, 0) 23%,
    rgba(255, 255, 255, 0) 55%,
    rgba(255, 255, 255, 1) 64%,
    rgba(255, 255, 255, 0) 73%
  );
  opacity: 0.15;
}

.VCU .ellipse-8 {
  position: absolute;
  width: 14px;
  height: 14px;
  top: 10px;
  left: 10px;
  background-color: #1f3a4c;
  border-radius: 7.03px;
  border: 0.39px solid;
  border-color: #000000;
}

.VCU .ellipse-9 {
  position: absolute;
  width: 22px;
  height: 22px;
  top: 6px;
  left: 6px;
}

.VCU .ellipse-10 {
  position: absolute;
  width: 16px;
  height: 16px;
  top: 9px;
  left: 9px;
}

.VCU .ellipse-11 {
  position: absolute;
  width: 19px;
  height: 19px;
  top: 7px;
  left: 7px;
}

.VCU .rectangle-3 {
  position: absolute;
  width: 31px;
  height: 18px;
  top: 8px;
  left: 0;
}

.VCU .vector {
  position: absolute;
  width: 11px;
  height: 1px;
  top: 8px;
  left: 2px;
}

.VCU .ellipse-12 {
  position: absolute;
  width: 13px;
  height: 2px;
  top: 18px;
  left: 7px;
}

.VCU .ellipse-13 {
  position: absolute;
  width: 10px;
  height: 1px;
  top: 17px;
  left: 15px;
  border-radius: 4.84px / 0.22px;
  filter: blur(0.44px);
  background: radial-gradient(
    50% 50% at 50% 50%,
    rgba(0, 0, 0, 0.5) 0%,
    rgba(0, 0, 0, 0) 100%
  );
}

.VCU .union {
  position: absolute;
  width: 18px;
  height: 19px;
  top: 5px;
  left: 7px;
}

.VCU .frame-14 {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2.64px;
  position: relative;
  flex: 1;
  flex-grow: 1;
  overflow: hidden;
}

.VCU .frame-15 {
  display: flex;
  width: 103.76px;
  align-items: flex-start;
  justify-content: center;
  gap: 1.76px;
  position: relative;
  flex: 0 0 auto;
  margin-right: -7.12px;
}

.VCU .text-wrapper-18 {
  position: relative;
  flex: 1;
  height: 8.79px;
  margin-top: -0.44px;
  font-family: "HarmonyOS Sans-Medium", Helvetica;
  font-weight: 500;
  color: var(--dark-colortext-iconprimary);
  font-size: 5.3px;
  letter-spacing: 0;
  line-height: 7px;
}

.VCU .text-wrapper-19 {
  position: relative;
  width: 38.25px;
  height: 8.79px;
  font-family: "HarmonyOS Sans-Regular", Helvetica;
  font-weight: 400;
  color: var(--dark-colortext-icontertiary);
  font-size: 5.3px;
  letter-spacing: 0;
  line-height: 7px;
}

.VCU .line {
  display: inline-flex;
  height: 5.28px;
  align-items: center;
  gap: 3.52px;
  padding: 0.37px 0px;
  position: relative;
  margin-right: -110.89px;
}

.VCU .frame-16 {
  display: inline-flex;
  align-items: center;
  gap: 0.88px;
  position: relative;
  flex: 0 0 auto;
  margin-top: -0.37px;
  margin-bottom: -0.37px;
}

.VCU .rectangle-4 {
  position: relative;
  width: 1px;
  height: 2.2px;
  background-color: #55e5c5;
  border-radius: 3.52px;
}

.VCU .rectangle-5 {
  position: relative;
  width: 1px;
  height: 5.28px;
  background-color: #55e5c5;
  border-radius: 3.52px;
}

.VCU .rectangle-6 {
  height: 3.52px;
  position: relative;
  width: 1px;
  background-color: #55e5c5;
  border-radius: 3.52px;
}

.VCU .rectangle-7 {
  height: 1px;
  position: relative;
  width: 1px;
  background-color: #55e5c5;
  border-radius: 3.52px;
}

.VCU .rectangle-8 {
  height: 3.08px;
  position: relative;
  width: 1px;
  background-color: #55e5c5;
  border-radius: 3.52px;
}

.VCU .rectangle-9 {
  height: 1.76px;
  position: relative;
  width: 1px;
  background-color: #55e5c5;
  border-radius: 3.52px;
}

.VCU .rectangle-10 {
  height: 4.4px;
  position: relative;
  width: 1px;
  background-color: #55e5c5;
  border-radius: 3.52px;
}

.VCU .rectangle-11 {
  height: 2.64px;
  position: relative;
  width: 1px;
  background-color: #55e5c5;
  border-radius: 3.52px;
}

.VCU .rectangle-12 {
  position: relative;
  width: 1px;
  height: 4.4px;
  background-color: var(--dark-colortext-icondisabled);
  border-radius: 3.52px;
}

.VCU .rectangle-13 {
  height: 2.64px;
  position: relative;
  width: 1px;
  background-color: var(--dark-colortext-icondisabled);
  border-radius: 3.52px;
}

.VCU .rectangle-14 {
  height: 1.76px;
  position: relative;
  width: 1px;
  background-color: var(--dark-colortext-icondisabled);
  border-radius: 3.52px;
}

.VCU .rectangle-15 {
  height: 3.08px;
  position: relative;
  width: 1px;
  background-color: var(--dark-colortext-icondisabled);
  border-radius: 3.52px;
}

.VCU .rectangle-16 {
  height: 2.2px;
  position: relative;
  width: 1px;
  background-color: var(--dark-colortext-icondisabled);
  border-radius: 3.52px;
}

.VCU .rectangle-17 {
  height: 3.52px;
  position: relative;
  width: 1px;
  background-color: var(--dark-colortext-icondisabled);
  border-radius: 3.52px;
}

.VCU .rectangle-18 {
  height: 1px;
  position: relative;
  width: 1px;
  background-color: var(--dark-colortext-icondisabled);
  border-radius: 3.52px;
}

.VCU .rectangle-19 {
  height: 5.28px;
  position: relative;
  width: 1px;
  background-color: var(--dark-colortext-icondisabled);
  border-radius: 3.52px;
}

.VCU .rectangle-20 {
  position: relative;
  width: 1px;
  height: 1.76px;
  margin-right: -0.56px;
  background-color: var(--dark-colortext-icondisabled);
  border-radius: 3.52px;
}

.VCU .highlight-wrapper {
  position: absolute;
  width: 43px;
  height: 42px;
  top: 0;
  left: 0;
}

.VCU .highlight {
  height: 42px;
}

.VCU .overlap-5 {
  position: relative;
  width: 43px;
  height: 42px;
  border-radius: 7.09px;
}

.VCU .frame-17 {
  position: absolute;
  width: 29px;
  height: 27px;
  top: 7px;
  left: 7px;
}

.VCU .icon-music-normal {
  position: relative;
  width: 30px;
  height: 30px;
  top: -1px;
  overflow: hidden;
  background-size: 100% 100%;
}

.VCU .ellipse-14 {
  position: absolute;
  width: 5px;
  height: 6px;
  top: 13px;
  left: 10px;
  background-color: #34ffc299;
  border-radius: 2.55px / 2.98px;
  filter: blur(3.19px);
}

.VCU .ellipse-15 {
  position: absolute;
  width: 5px;
  height: 6px;
  top: 14px;
  left: 15px;
  background-color: #00473299;
  border-radius: 2.55px / 2.98px;
  filter: blur(3.19px);
}

.VCU .ellipse-16 {
  position: absolute;
  width: 12px;
  height: 15px;
  top: 8px;
  left: 10px;
}

.VCU .frame-18 {
  position: absolute;
  width: 23px;
  height: 23px;
  top: 0;
  left: 3px;
}

.VCU .overlap-group-5 {
  position: relative;
  width: 32px;
  height: 32px;
  top: -4px;
  left: -5px;
}

.VCU .ellipse-17 {
  position: absolute;
  width: 23px;
  height: 23px;
  top: 5px;
  left: 5px;
}

.VCU .ellipse-18 {
  position: absolute;
  width: 25px;
  height: 25px;
  top: 4px;
  left: 4px;
  border-radius: 12.27px;
  background: conic-gradient(
    from 270deg at 50% 50%,
    rgba(255, 255, 255, 0.04) 3%,
    rgba(255, 255, 255, 0) 5%,
    rgba(255, 255, 255, 0.58) 11%,
    rgba(255, 255, 255, 0.68) 19%,
    rgba(255, 255, 255, 0) 23%,
    rgba(255, 255, 255, 0) 55%,
    rgba(255, 255, 255, 1) 62%,
    rgba(255, 255, 255, 1) 64%,
    rgba(255, 255, 255, 1) 66%,
    rgba(255, 255, 255, 0) 73%
  );
  opacity: 0.12;
}

.VCU .ellipse-19 {
  position: absolute;
  width: 25px;
  height: 25px;
  top: 4px;
  left: 4px;
  border-radius: 12.27px;
  transform: rotate(23.7deg);
  background: conic-gradient(
    from 270deg at 50% 50%,
    rgba(255, 255, 255, 0.04) 3%,
    rgba(255, 255, 255, 0) 5%,
    rgba(255, 255, 255, 0.58) 11%,
    rgba(255, 255, 255, 0.68) 19%,
    rgba(255, 255, 255, 0) 23%,
    rgba(255, 255, 255, 0) 55%,
    rgba(255, 255, 255, 1) 62%,
    rgba(255, 255, 255, 1) 64%,
    rgba(255, 255, 255, 1) 66%,
    rgba(255, 255, 255, 0) 73%
  );
  opacity: 0.12;
}

.VCU .ellipse-20 {
  position: absolute;
  width: 25px;
  height: 25px;
  top: 4px;
  left: 4px;
  border-radius: 12.27px;
  transform: rotate(90deg);
  background: conic-gradient(
    from 270deg at 50% 50%,
    rgba(255, 255, 255, 0.04) 3%,
    rgba(255, 255, 255, 0) 5%,
    rgba(255, 255, 255, 0.58) 11%,
    rgba(255, 255, 255, 0.68) 19%,
    rgba(255, 255, 255, 0) 23%,
    rgba(255, 255, 255, 0) 55%,
    rgba(255, 255, 255, 1) 64%,
    rgba(255, 255, 255, 0) 73%
  );
  opacity: 0.15;
}

.VCU .ellipse-21 {
  position: absolute;
  width: 14px;
  height: 14px;
  top: 9px;
  left: 9px;
  background-color: #1f3a4c;
  border-radius: 6.8px;
  border: 0.38px solid;
  border-color: #000000;
}

.VCU .ellipse-22 {
  position: absolute;
  width: 21px;
  height: 21px;
  top: 6px;
  left: 6px;
}

.VCU .ellipse-23 {
  position: absolute;
  width: 16px;
  height: 16px;
  top: 8px;
  left: 8px;
}

.VCU .ellipse-24 {
  position: absolute;
  width: 18px;
  height: 18px;
  top: 7px;
  left: 7px;
}

.VCU .rectangle-21 {
  position: absolute;
  width: 30px;
  height: 17px;
  top: 8px;
  left: 0;
}

.VCU .ellipse-25 {
  position: absolute;
  width: 12px;
  height: 2px;
  top: 17px;
  left: 6px;
}

.VCU .ellipse-26 {
  position: absolute;
  width: 9px;
  height: 1px;
  top: 17px;
  left: 14px;
  border-radius: 4.68px / 0.21px;
  filter: blur(0.43px);
  background: radial-gradient(
    50% 50% at 50% 50%,
    rgba(0, 0, 0, 0.5) 0%,
    rgba(0, 0, 0, 0) 100%
  );
}

.VCU .union-2 {
  position: absolute;
  width: 18px;
  height: 18px;
  top: 5px;
  left: 6px;
}

.VCU .rectangle-22 {
  position: absolute;
  width: 34px;
  height: 32px;
  top: 5px;
  left: 5px;
  border-radius: 2.36px;
  border: 2.36px solid;
  border-color: #55e5c5;
  backdrop-filter: blur(3.33px) brightness(100%);
  -webkit-backdrop-filter: blur(3.33px) brightness(100%);
}

.VCU .rectangle-23 {
  position: absolute;
  width: 30px;
  height: 29px;
  top: 6px;
  left: 6px;
  border-radius: 2.6px;
  border: 0.62px solid;
  border-color: #21282d;
  backdrop-filter: blur(3.11px) brightness(100%);
  -webkit-backdrop-filter: blur(3.11px) brightness(100%);
}

.VCU .rectangle-24 {
  position: absolute;
  width: 39px;
  height: 37px;
  top: 2px;
  left: 2px;
  border-radius: 4.73px;
  border: 2.36px solid;
  border-color: #55e5c5;
  opacity: 0.5;
}

.VCU .rectangle-25 {
  position: absolute;
  width: 43px;
  height: 42px;
  top: 0;
  left: 0;
  border-radius: 7.09px;
  border: 2.36px solid;
  border-color: #55e5c5;
  opacity: 0.2;
}

.VCU .wincursor-default {
  position: absolute;
  width: 32px;
  height: 19px;
  top: 23px;
  left: 19px;
}

.VCU .element {
  position: absolute;
  width: 63px;
  height: 51px;
  top: 101px;
  left: 330px;
  object-fit: cover;
}

.VCU .text-wrapper-20 {
  position: relative;
  width: 216px;
  margin-top: -1px;
  font-family: "Outfit", Helvetica;
  font-weight: 700;
  color: #000000;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 18px;
  text-decoration: underline;
}

.VCU .frame-19 {
  height: 200px;
  top: 216px;
  left: 284px;
  background-color: #e0deff;
  border-radius: 10px;
  border: 1px solid;
  border-color: #7859f680;
  position: absolute;
  width: 268px;
  overflow: hidden;
}

.VCU .overlap-6 {
  position: relative;
  height: 150px;
  background-size: cover;
  background-position: 50% 50%;
}

.VCU .frame-20 {
  height: 150px;
  top: 0;
  left: 0;
  background-color: #1e2125;
  position: absolute;
  width: 268px;
  overflow: hidden;
}

.VCU .frame-21 {
  position: relative;
  height: 286px;
  top: -61px;
  background-color: #000000;
  border-radius: 9.53px;
  overflow: hidden;
}

.VCU .overlap-7 {
  position: relative;
  width: 602px;
  height: 401px;
  top: -23px;
  left: -177px;
}

.VCU .group-5 {
  position: absolute;
  width: 602px;
  height: 401px;
  top: 0;
  left: 0;
}

.VCU .overlap-8 {
  position: absolute;
  width: 268px;
  height: 150px;
  top: 84px;
  left: 177px;
  background-size: 100% 100%;
}

.VCU .group-6 {
  position: absolute;
  width: 65px;
  height: 25px;
  top: 20px;
  left: 124px;
}

.VCU .overlap-group-6 {
  position: relative;
  height: 25px;
}

.VCU .group-7 {
  position: absolute;
  width: 54px;
  height: 20px;
  top: 3px;
  left: 11px;
  background-color: #50e3c233;
  border-radius: 18.49px;
  border: 0.74px solid;
  border-color: #ffffff33;
  backdrop-filter: blur(3.14px) brightness(100%);
  -webkit-backdrop-filter: blur(3.14px) brightness(100%);
}

.VCU .union-3 {
  position: absolute;
  width: 31px;
  height: 10px;
  top: 4px;
  left: 15px;
}

.VCU .element-play {
  position: absolute;
  width: 9px;
  height: 9px;
  top: 5px;
  left: 3px;
}

.VCU .mask-group-wrapper {
  position: absolute;
  width: 25px;
  height: 25px;
  top: 0;
  left: 0;
  border-radius: 12.75px;
  box-shadow: 0px 0.99px 2.01px #00000040;
}

.VCU .mask-group {
  position: absolute;
  width: 24px;
  height: 24px;
  top: 1px;
  left: 1px;
}

.VCU .group-8 {
  position: absolute;
  width: 73px;
  height: 29px;
  top: 87px;
  left: 38px;
}

.VCU .overlap-9 {
  position: relative;
  height: 29px;
}

.VCU .group-9 {
  width: 60px;
  height: 22px;
  top: 3px;
  left: 13px;
  background-color: #50e3c280;
  border-radius: 20.73px;
  border: 0.83px solid;
  backdrop-filter: blur(3.52px) brightness(100%);
  -webkit-backdrop-filter: blur(3.52px) brightness(100%);
  position: absolute;
  border-color: #ffffff99;
}

.VCU .union-4 {
  position: absolute;
  width: 35px;
  height: 11px;
  top: 5px;
  left: 17px;
}

.VCU .element-play-2 {
  position: absolute;
  width: 10px;
  height: 10px;
  top: 5px;
  left: 4px;
}

.VCU .group-10 {
  width: 29px;
  height: 29px;
  border-radius: 14.29px;
  box-shadow: 0px 1.11px 2.25px #00000040;
  position: absolute;
  top: 0;
  left: 0;
}

.VCU .mask-group-2 {
  width: 27px;
  height: 27px;
  position: absolute;
  top: 1px;
  left: 1px;
}

.VCU .group-11 {
  position: absolute;
  width: 101px;
  height: 40px;
  top: 97px;
  left: 152px;
}

.VCU .overlap-10 {
  position: relative;
  height: 40px;
}

.VCU .group-12 {
  width: 84px;
  height: 31px;
  top: 4px;
  left: 17px;
  background-color: #50e3c299;
  border-radius: 28.94px;
  border: 1.16px solid;
  backdrop-filter: blur(4.92px) brightness(100%);
  -webkit-backdrop-filter: blur(4.92px) brightness(100%);
  position: absolute;
  border-color: #ffffff99;
}

.VCU .union-5 {
  position: absolute;
  width: 49px;
  height: 16px;
  top: 7px;
  left: 23px;
}

.VCU .element-play-3 {
  position: absolute;
  width: 14px;
  height: 14px;
  top: 7px;
  left: 5px;
}

.VCU .group-13 {
  width: 40px;
  height: 40px;
  border-radius: 19.95px;
  box-shadow: 0px 1.55px 3.14px #00000040;
  position: absolute;
  top: 0;
  left: 0;
}

.VCU .mask-group-3 {
  width: 37px;
  height: 37px;
  position: absolute;
  top: 1px;
  left: 1px;
}

.VCU .union-6 {
  position: absolute;
  width: 197px;
  height: 29px;
  top: -1528px;
  left: -1715px;
}

.VCU .rectangle-26 {
  position: absolute;
  width: 268px;
  height: 119px;
  top: 189px;
  left: 177px;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 1) 100%
  );
  opacity: 0.5;
}

.VCU .AI-sound-effect {
  position: absolute;
  top: 270px;
  left: 223px;
  font-family: "Outfit", Helvetica;
  font-weight: 400;
  color: var(--generalwhite);
  font-size: 14.3px;
  text-align: center;
  letter-spacing: 0;
  line-height: 19.1px;
  white-space: nowrap;
}

.VCU .text-wrapper-21 {
  font-weight: 700;
}

.VCU .frame-22 {
  position: absolute;
  width: 268px;
  height: 200px;
  top: 637px;
  left: 24px;
  background-color: #e0deff;
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid;
  border-color: #7859f680;
}

.VCU .frame-23 {
  position: absolute;
  width: 268px;
  height: 200px;
  top: 637px;
  left: 308px;
  background-color: #e0deff;
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid;
  border-color: #7859f680;
}

.VCU .union-7 {
  position: absolute;
  width: 197px;
  height: 29px;
  top: -1745px;
  left: -1715px;
}

.VCU .text-wrapper-22 {
  position: absolute;
  top: 147px;
  left: 33px;
  font-family: "Outfit", Helvetica;
  font-weight: 400;
  color: #000000;
  font-size: 18px;
  text-align: center;
  letter-spacing: 0;
  line-height: 24px;
  white-space: nowrap;
}

.VCU .frame-24 {
  position: absolute;
  width: 600px;
  height: 392px;
  top: 228px;
  left: 0;
  background-color: #dfdcff;
}

.VCU .overlap-11 {
  position: relative;
  height: 392px;
}

.VCU .frame-25 {
  position: absolute;
  width: 552px;
  height: 392px;
  top: 0;
  left: 24px;
  border-radius: 12px;
  overflow: hidden;
  background: linear-gradient(
      45deg,
      rgba(255, 114, 224, 1) 0%,
      rgba(144, 98, 255, 1) 55%,
      rgba(87, 144, 255, 1) 100%
    ),
    linear-gradient(0deg, rgba(119, 10, 246, 1) 0%, rgba(119, 10, 246, 1) 100%),
    linear-gradient(
      0deg,
      rgba(255, 255, 255, 1) 0%,
      rgba(255, 255, 255, 1) 100%
    );
}

.VCU .element-wrapper {
  position: absolute;
  width: 544px;
  height: 320px;
  top: 68px;
  left: 4px;
  border-radius: 8px;
  overflow: hidden;
}

.VCU .element-2 {
  position: relative;
  top: -36px;
  left: -28px;
  background-color: #c4c4c4;
  width: 600px;
  height: 392px;
}

.VCU .frame-26 {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  position: absolute;
  top: 16px;
  left: 32px;
}

.VCU .frame-27 {
  position: relative;
  width: 36px;
  height: 36px;
  background-color: #ffffff;
  border-radius: 34px;
  overflow: hidden;
  border: 1px solid;
}

.VCU .overlap-group-7 {
  position: relative;
  width: 40px;
  height: 40px;
  top: -2px;
  left: -2px;
  border-radius: 2.13px;
}

.VCU .girl {
  position: absolute;
  width: 36px;
  height: 36px;
  top: 2px;
  left: 2px;
  object-fit: cover;
}

.VCU .avatar {
  position: absolute;
  width: 40px;
  height: 40px;
  top: 0;
  left: 0;
  background-color: #fdd7ff;
  border-radius: 2.13px;
}

.VCU .avatar-2 {
  position: absolute;
  width: 20px;
  height: 24px;
  top: 8px;
  left: 10px;
}

.VCU .frame-28 {
  display: inline-flex;
  align-items: flex-start;
  gap: 6.25px;
  padding: 8px 15px;
  position: relative;
  flex: 0 0 auto;
  border-radius: 20px 20px 20px 0px;
  background: linear-gradient(
    0deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.3) 100%
  );
}

.VCU .text-wrapper-23 {
  position: relative;
  width: fit-content;
  margin-top: -0.62px;
  font-family: "HarmonyOS Sans-Medium", Helvetica;
  font-weight: 500;
  color: #ffffff;
  font-size: 14px;
  text-align: center;
  letter-spacing: 0;
  line-height: 20px;
  white-space: nowrap;
}

.VCU .frame-29 {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  position: absolute;
  top: 16px;
  left: 297px;
}

.VCU .frame-30 {
  display: inline-flex;
  align-items: flex-start;
  gap: 6.25px;
  padding: 8px 15px;
  position: relative;
  flex: 0 0 auto;
  border-radius: 35px 20px 0px 35px;
  border: 1px solid;
  border-color: #ffffff42;
  background: linear-gradient(
      90deg,
      rgba(255, 186, 245, 1) 0%,
      rgba(203, 181, 255, 1) 55%,
      rgba(151, 204, 251, 1) 100%
    ),
    linear-gradient(0deg, rgba(119, 10, 246, 1) 0%, rgba(119, 10, 246, 1) 100%),
    linear-gradient(0deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 1) 100%);
}

.VCU .text-wrapper-24 {
  position: relative;
  width: fit-content;
  margin-top: -1px;
  font-family: "HarmonyOS Sans-Bold", Helvetica;
  font-weight: 700;
  color: #40008c;
  font-size: 14px;
  text-align: center;
  letter-spacing: 0;
  line-height: 20px;
  white-space: nowrap;
}

.VCU .avatar-image-wrapper {
  position: relative;
  height: 36px;
  background-size: cover;
  background-position: 50% 50%;
}

.VCU .avatar-image {
  position: absolute;
  width: 36px;
  height: 36px;
  top: 0;
  left: 0;
  object-fit: cover;
}

.VCU .element-3 {
  position: absolute;
  top: 0;
  left: 0;
  object-fit: cover;
  width: 600px;
  height: 392px;
}
/* Original CSS code should be injected here */

.VCU {
  display: flex;
  justify-content: center;
  width: 100%;
}

.content {
  max-width: 600px;
  width: 100%;
  overflow: hidden;
  position: relative;
}

.frame {
  display: flex;
  align-items: flex-start;
  padding: 16px 10px 16px 32px;
  background-color: #ffffff;
}

.lockup {
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

.lockup-producticon {
  width: 32px;
  height: 32px;
}

.ico-icon {
  height: 32px;
  box-shadow: inset 0px 0px 0.12px #0000003d;
}

.mask {
  width: 32px;
  height: 32px;
  border-radius: 7.3px;
  background-image: url(https://c.animaapp.com/mcujcvmlUBXVNG/img/producticon.svg);
  background-size: 100% 100%;
}

.lockup-productname {
  display: inline-flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  gap: 4px;
}

.lockup-companyname {
  display: inline-flex;
  align-items: center;
  padding: 0px 0px 0px 1px;
}

.text-wrapper {
  font-family: "Messina Sans-Regular", Helvetica;
  font-weight: 400;
  color: #000000;
  font-size: 12px;
  letter-spacing: -0.48px;
  line-height: 12px;
}

.product-wrapper {
  display: inline-flex;
  align-items: center;
}

.product {
  font-family: "Messina Sans-Bold", Helvetica;
  font-weight: 700;
  color: #000000;
  font-size: 20px;
  letter-spacing: -0.8px;
  line-height: normal;
}

.hero {
  padding: 40px 24px;
  text-align: center;
}

.edit-boldly-create {
  font-family: "Outfit", Helvetica;
  font-weight: 700;
  color: #000000;
  font-size: 36px;
  line-height: 42px;
  margin-bottom: 20px;
}

.filmora-your-easy {
  font-family: "Outfit", Helvetica;
  font-weight: 400;
  font-size: 18px;
  line-height: 24px;
  margin-bottom: 40px;
}

.text-wrapper-9 {
  font-weight: 700;
  color: #770af6;
}

.text-wrapper-10 {
  color: #000000;
}

.frame-24 {
  position: relative;
  width: 100%;
  height: 392px;
  overflow: hidden;
}

.frame-25 {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  background: linear-gradient(
    45deg,
    rgba(255, 114, 224, 1) 0%,
    rgba(144, 98, 255, 1) 55%,
    rgba(87, 144, 255, 1) 100%
  );
}

.element-wrapper {
  position: absolute;
  width: calc(100% - 8px);
  height: calc(100% - 72px);
  top: 68px;
  left: 4px;
  border-radius: 8px;
  overflow: hidden;
}

.element-2 {
  width: 100%;
  height: 100%;
  background-color: #c4c4c4;
}

.frame-26,
.frame-29 {
  display: flex;
  align-items: center;
  gap: 10px;
  position: absolute;
  top: 16px;
}

.frame-26 {
  left: 32px;
}

.frame-29 {
  right: 32px;
}

.frame-27 {
  width: 36px;
  height: 36px;
  background-color: #ffffff;
  border-radius: 50%;
  overflow: hidden;
  border: 1px solid;
}

.frame-28,
.frame-30 {
  display: inline-flex;
  align-items: center;
  padding: 8px 15px;
  border-radius: 20px;
}

.frame-28 {
  background: rgba(255, 255, 255, 0.3);
}

.frame-30 {
  background: linear-gradient(
    90deg,
    rgba(255, 186, 245, 1) 0%,
    rgba(203, 181, 255, 1) 55%,
    rgba(151, 204, 251, 1) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.26);
}

.text-wrapper-23,
.text-wrapper-24 {
  font-family: "HarmonyOS Sans-Medium", Helvetica;
  font-size: 14px;
  line-height: 20px;
}

.text-wrapper-23 {
  color: #ffffff;
}

.text-wrapper-24 {
  font-weight: 700;
  color: #40008c;
}

.element-3 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cta {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

.frame-2 {
  display: inline-flex;
  align-items: center;
  gap: 16px;
}

.filled-large-icon,
.div-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 172px;
  height: 56px;
  border-radius: 8px;
  font-family: "HarmonyOS Sans-Bold", Helvetica;
  font-weight: 700;
  font-size: 18px;
  text-align: center;
  letter-spacing: 0;
  line-height: 26px;
}

.filled-large-icon {
  background-color: #7859f6;
  color: #ffffff;
}

.div-wrapper {
  border: 2px solid #7859f6;
  color: #7859f6;
}

.features {
  margin-top: 60px;
  padding: 0 24px;
}

.advanced-features {
  font-family: "Outfit", Helvetica;
  font-weight: 600;
  font-size: 28px;
  line-height: 32px;
  text-align: center;
  margin-bottom: 40px;
}

.text-wrapper-13 {
  font-family: "HarmonyOS Sans-Bold", Helvetica;
  font-weight: 700;
  color: #41008c;
  font-size: 16px;
  line-height: 22px;
  margin-bottom: 20px;
}

.feature-list {
  display: flex;
  justify-content: space-between;
  font-family: "HarmonyOS Sans-Regular", Helvetica;
  font-weight: 400;
  color: #41008c;
  font-size: 12px;
  line-height: 24px;
}

.group-wrapper {
  margin-top: 40px;
}

.overlap-group-2 {
  background-image: url(https://c.animaapp.com/mcujcvmlUBXVNG/img/subtract-1.svg);
  background-size: 100% 100%;
  padding: 30px;
  text-align: center;
}

.frame-3 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.text-wrapper-14 {
  font-family: "Outfit", Helvetica;
  font-weight: 700;
  color: #ffffff;
  font-size: 24px;
  line-height: 28px;
}

.after-the-trial-auto {
  font-family: "HarmonyOS Sans-Regular", Helvetica;
  font-size: 14px;
  line-height: 22px;
  margin-top: 20px;
}

.text-wrapper-15 {
  font-family: "HarmonyOS Sans-Bold", Helvetica;
  font-weight: 700;
  color: #770af6;
}

.text-wrapper-16 {
  font-family: "Outfit", Helvetica;
  font-weight: 800;
  font-size: 30px;
  line-height: 28px;
  background: linear-gradient(
    90deg,
    rgba(255, 114, 224, 1) 0%,
    rgba(144, 98, 255, 1) 55%,
    rgba(87, 144, 255, 1) 100%
  );
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
}

.ai-features {
  background-color: #ffffff;
  padding: 40px 24px;
}

.enjoy-the-AI-video {
  font-family: "Outfit", Helvetica;
  font-weight: 700;
  font-size: 32px;
  line-height: 42px;
  text-align: center;
  margin-bottom: 40px;
}

.frame-5 {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 40px;
}

.filled-large-icon-2 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 188px;
  height: 56px;
  background-color: #7859f6;
  border-radius: 8px;
  color: #ffffff;
  font-family: "HarmonyOS Sans-Bold", Helvetica;
  font-weight: 700;
  font-size: 18px;
}

.windows,
.icon-windows {
  width: 24px;
  height: 24px;
}

.group-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 40px;
}

.frame-6,
.frame-9,
.frame-10,
.frame-22,
.frame-23 {
  background-color: #e0deff;
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid #7859f680;
}

.frame-7 {
  height: 150px;
  background-color: #000000;
  overflow: hidden;
}

.img-2 {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.frame-8 {
  padding: 16px;
  text-align: center;
}

.text-wrapper-17,
.text-wrapper-20 {
  font-family: "Outfit", Helvetica;
  font-weight: 700;
  color: #000000;
  font-size: 16px;
  line-height: 18px;
  text-decoration: underline;
}

.text-wrapper-22 {
  font-family: "Outfit", Helvetica;
  font-weight: 400;
  color: #000000;
  font-size: 18px;
  line-height: 24px;
  text-align: center;
}

.view {
  background-color: var(--labellightprimary);
  padding: 40px 32px;
}

.brand {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.main-vertical {
  width: 120px;
  height: 72px;
}

.socialapp-group {
  display: flex;
  gap: 13px;
}

.social-app {
  width: 24px;
  height: 24px;
}

.caption {
  font-family: var(--caption-font-family);
  font-weight: var(--caption-font-weight);
  font-size: var(--caption-font-size);
  line-height: var(--caption-line-height);
  color: var(--labeldarksecondary);
}

.you-are-receiving,
.please-do-not-reply,
.about-wondershare,
.p {
  margin-bottom: 8px;
}

.text-wrapper-2,
.text-wrapper-4,
.text-wrapper-5 {
  text-decoration: underline;
}

@media (max-width: 600px) {
  .content {
    width: 100%;
  }

  .frame-2,
  .group-2 {
    flex-direction: column;
    align-items: center;
  }

  .filled-large-icon,
  .div-wrapper,
  .filled-large-icon-2 {
    width: 100%;
  }

  .group-2 {
    grid-template-columns: 1fr;
  }
}
