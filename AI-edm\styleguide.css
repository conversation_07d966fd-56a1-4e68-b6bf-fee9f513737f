:root {
  --filmora-9-teal: rgba(85, 229, 197, 1);
  --white: rgba(255, 255, 255, 1);
  --wondershare-white: rgba(255, 255, 255, 1);
  --lighttertiary: rgba(0, 0, 0, 0.4);
  --labeldarksecondary: rgba(153, 153, 153, 1);
  --labellightprimary: rgba(0, 0, 0, 1);
  --dark-colorbackgroundoverlay3: rgba(46, 53, 58, 1);
  --filmorasecondary-3: rgba(80, 227, 194, 1);
  --filmora-x-primary: rgba(85, 229, 197, 1);
  --dark-colorbrandprimary: rgba(85, 229, 197, 1);
  --dark-colorcommonblack: rgba(0, 0, 0, 1);
  --dark-colorcomponentmask: rgba(0, 0, 0, 0.6);
  --dark-colortext-iconprimary: rgba(240, 248, 254, 1);
  --dark-colortext-icontertiary: rgba(149, 156, 162, 1);
  --dark-colortext-icondisabled: rgba(79, 87, 93, 1);
  --dark-colortext-iconsecondary: rgba(195, 202, 208, 1);
  --dark-colorstatedisabled1: rgba(154, 179, 202, 0.08);
  --dark-colorstatehover2: rgba(202, 232, 255, 0.14);
  --dark-colorstatepressed1: rgba(199, 212, 224, 0.14);
  --dark-colorcommonwhite: rgba(255, 255, 255, 1);
  --generalwhite: rgba(255, 255, 255, 1);
  --caption-font-family: "Segoe UI", Helvetica;
  --caption-font-weight: 400;
  --caption-font-size: 12px;
  --caption-letter-spacing: 0px;
  --caption-line-height: 16px;
  --caption-font-style: normal;
  --shadow-small: 0px 4px 8px 0px rgba(0, 0, 0, 0.2);
}
