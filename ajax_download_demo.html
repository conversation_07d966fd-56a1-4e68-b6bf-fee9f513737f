<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AJAX File Download Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        /* 背景动画粒子 */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 0.8; }
        }

        .progress-container {
            width: 400px;
            padding: 40px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: slideIn 0.8s ease-out;
            position: relative;
            overflow: hidden;
        }

        @keyframes slideIn {
            from { transform: translateY(50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .progress-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: rotate(45deg);
            animation: shine 3s ease-in-out infinite;
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
            100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        }

        .title {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
            font-size: 24px;
            font-weight: 600;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .progress-bar {
            width: 100%;
            height: 12px;
            background: linear-gradient(90deg, #f0f0f0, #e0e0e0);
            border-radius: 20px;
            overflow: hidden;
            position: relative;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .progress {
            height: 100%;
            background: linear-gradient(90deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
            background-size: 400% 400%;
            width: 0%;
            border-radius: 20px;
            position: relative;
            animation: gradientShift 3s ease infinite;
            transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .progress::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: progressShine 2s ease-in-out infinite;
        }

        @keyframes progressShine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .stats {
            margin: 25px 0;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .stat-item {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 15px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        }

        .stat-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-value {
            font-size: 18px;
            font-weight: 700;
            color: #333;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .progress-text {
            grid-column: 1 / -1;
            font-size: 24px;
            font-weight: 800;
            text-align: center;
            margin-top: 10px;
        }

        .buttons {
            display: flex;
            gap: 15px;
            margin-top: 25px;
        }

        button {
            flex: 1;
            padding: 15px 25px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .download-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
        }

        .download-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 12px 24px rgba(102, 126, 234, 0.4);
        }

        .cancel-btn {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            box-shadow: 0 8px 16px rgba(255, 107, 107, 0.3);
        }

        .cancel-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 12px 24px rgba(255, 107, 107, 0.4);
        }

        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .status-message {
            margin-top: 20px;
            padding: 15px;
            border-radius: 12px;
            text-align: center;
            font-weight: 600;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s ease;
        }

        .status-message.show {
            opacity: 1;
            transform: translateY(0);
        }

        .status-success {
            background: linear-gradient(135deg, #51cf66, #40c057);
            color: white;
        }

        .status-error {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
        }

        .status-info {
            background: linear-gradient(135deg, #339af0, #228be6);
            color: white;
        }

        /* 下载动画效果 */
        .downloading {
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7); }
            70% { box-shadow: 0 0 0 20px rgba(102, 126, 234, 0); }
            100% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0); }
        }
    </style>
</head>
<body>
    <!-- 背景粒子动画 -->
    <div class="particles" id="particles"></div>

    <div class="progress-container" id="container">
        <h1 class="title">🚀 File Download Center</h1>

        <div class="progress-bar">
            <div class="progress" id="progressBar"></div>
        </div>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-label">File Size</div>
                <div class="stat-value" id="fileSize">0 MB</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">Download Speed</div>
                <div class="stat-value" id="downloadSpeed">0 MB/s</div>
            </div>
            <div class="stat-item progress-text">
                <div class="stat-label">Progress</div>
                <div class="stat-value" id="progressPercent">0%</div>
            </div>
        </div>

        <div class="buttons">
            <button id="downloadBtn" class="download-btn" onclick="startDownload()">
                📥 Start Download
            </button>
            <button id="cancelBtn" class="cancel-btn" onclick="cancelDownload()" disabled>
                ❌ Cancel
            </button>
        </div>

        <div class="status-message" id="statusMessage"></div>
    </div>

    <script>
        let xhr = null;
        let startTime = null;
        let downloadStartTime = null;

        // 埋点函数
        function trackEvent(eventName, eventData = {}) {
            const timestamp = new Date().toISOString();
            const eventInfo = {
                event: eventName,
                timestamp: timestamp,
                userAgent: navigator.userAgent,
                url: window.location.href,
                ...eventData
            };

            console.log('📊 Analytics Event:', eventInfo);

            // 这里可以发送到实际的分析服务
            // fetch('/analytics', {
            //     method: 'POST',
            //     headers: { 'Content-Type': 'application/json' },
            //     body: JSON.stringify(eventInfo)
            // });
        }

        // 显示状态消息
        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('statusMessage');
            statusEl.textContent = message;
            statusEl.className = `status-message show status-${type}`;

            setTimeout(() => {
                statusEl.classList.remove('show');
            }, 3000);
        }

        // 创建背景粒子
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            for (let i = 0; i < 50; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        function startDownload() {
            // 埋点：下载开始
            trackEvent('download_started', {
                fileName: 'capcut_installer.exe',
                fileUrl: 'https://lf16-capcut.faceulv.com/obj/capcutpc-packages-sg/installer/capcut_capcutpc_0_1.2.6_installer.exe'
            });

            document.getElementById('downloadBtn').disabled = true;
            document.getElementById('cancelBtn').disabled = false;
            document.getElementById('fileSize').textContent = '0 MB';
            document.getElementById('downloadSpeed').textContent = '0 MB/s';
            document.getElementById('progressPercent').textContent = '0%';
            document.getElementById('progressBar').style.width = '0%';

            // 添加下载动画效果
            document.getElementById('container').classList.add('downloading');
            showStatus('🚀 Starting download...', 'info');

            const url = 'https://lf16-capcut.faceulv.com/obj/capcutpc-packages-sg/installer/capcut_capcutpc_0_1.2.6_installer.exe';
            xhr = new XMLHttpRequest();
            startTime = Date.now();
            downloadStartTime = startTime;
            let lastUpdateTime = startTime;
            let lastLoaded = 0;

            xhr.open('GET', url, true);
            xhr.responseType = 'blob';

            xhr.onprogress = function(event) {
                if (event.lengthComputable) {
                    const loaded = event.loaded;
                    const total = event.total;
                    const currentTime = Date.now();
                    const timeDiff = (currentTime - lastUpdateTime) / 1000;

                    // 计算瞬时速度 (MB/s)
                    const bytesInInterval = loaded - lastLoaded;
                    const speed = timeDiff > 0 ? (bytesInInterval / (1024 * 1024) / timeDiff).toFixed(2) : 0;

                    lastUpdateTime = currentTime;
                    lastLoaded = loaded;

                    const progress = (loaded / total * 100).toFixed(1);
                    const fileSize = (total / 1024 / 1024).toFixed(2);
                    const downloadedSize = (loaded / 1024 / 1024).toFixed(2);

                    document.getElementById('fileSize').textContent = `${downloadedSize}/${fileSize} MB`;
                    document.getElementById('downloadSpeed').textContent = `${speed} MB/s`;
                    document.getElementById('progressPercent').textContent = `${progress}%`;
                    document.getElementById('progressBar').style.width = `${progress}%`;

                    // 埋点：下载进度（每25%记录一次）
                    const progressInt = Math.floor(progress);
                    if (progressInt > 0 && progressInt % 25 === 0 && !window[`progress_${progressInt}_tracked`]) {
                        trackEvent('download_progress', {
                            progress: progressInt,
                            downloadSpeed: speed,
                            fileSize: fileSize,
                            elapsedTime: (currentTime - downloadStartTime) / 1000
                        });
                        window[`progress_${progressInt}_tracked`] = true;
                    }
                }
            };

            xhr.onload = function() {
                document.getElementById('container').classList.remove('downloading');

                if (this.status === 200) {
                    try {
                        const blob = new Blob([this.response], { type: 'application/octet-stream' });
                        const link = document.createElement('a');
                        link.href = window.URL.createObjectURL(blob);
                        link.download = 'capcut_installer.exe';

                        // 监听文件保存
                        link.addEventListener('click', function() {
                            // 埋点：下载完成
                            const totalTime = (Date.now() - downloadStartTime) / 1000;
                            trackEvent('download_completed', {
                                fileName: 'capcut_installer.exe',
                                fileSize: (this.response.size / 1024 / 1024).toFixed(2) + ' MB',
                                totalTime: totalTime,
                                averageSpeed: ((this.response.size / 1024 / 1024) / totalTime).toFixed(2) + ' MB/s'
                            });

                            showStatus('✅ Download completed successfully!', 'success');

                            // 延迟埋点：文件保存成功（模拟）
                            setTimeout(() => {
                                trackEvent('file_save_success', {
                                    fileName: 'capcut_installer.exe',
                                    saveLocation: 'Downloads'
                                });
                            }, 1000);
                        });

                        link.click();

                    } catch (error) {
                        // 埋点：文件保存失败
                        trackEvent('file_save_failed', {
                            error: error.message,
                            fileName: 'capcut_installer.exe'
                        });
                        showStatus('❌ Failed to save file', 'error');
                    }
                } else {
                    // 埋点：下载失败
                    trackEvent('download_failed', {
                        statusCode: this.status,
                        statusText: this.statusText,
                        elapsedTime: (Date.now() - downloadStartTime) / 1000
                    });
                    showStatus('❌ Download failed', 'error');
                }

                document.getElementById('downloadBtn').disabled = false;
                document.getElementById('cancelBtn').disabled = true;

                // 重置进度追踪标志
                for (let i = 25; i <= 100; i += 25) {
                    delete window[`progress_${i}_tracked`];
                }
            };

            xhr.onerror = function() {
                document.getElementById('container').classList.remove('downloading');

                // 埋点：下载错误
                trackEvent('download_error', {
                    error: 'Network error',
                    elapsedTime: (Date.now() - downloadStartTime) / 1000
                });

                document.getElementById('downloadSpeed').textContent = 'Error';
                document.getElementById('progressPercent').textContent = 'Error';
                document.getElementById('downloadBtn').disabled = false;
                document.getElementById('cancelBtn').disabled = true;
                showStatus('❌ Network error occurred', 'error');
            };

            xhr.send();
        }

        function cancelDownload() {
            if (xhr) {
                // 埋点：下载取消
                trackEvent('download_cancelled', {
                    elapsedTime: downloadStartTime ? (Date.now() - downloadStartTime) / 1000 : 0,
                    progress: document.getElementById('progressPercent').textContent
                });

                xhr.abort();
                document.getElementById('container').classList.remove('downloading');
                document.getElementById('downloadSpeed').textContent = 'Cancelled';
                document.getElementById('progressPercent').textContent = 'Cancelled';
                document.getElementById('progressBar').style.width = '0%';
                document.getElementById('downloadBtn').disabled = false;
                document.getElementById('cancelBtn').disabled = true;
                showStatus('⏹️ Download cancelled', 'info');

                // 重置进度追踪标志
                for (let i = 25; i <= 100; i += 25) {
                    delete window[`progress_${i}_tracked`];
                }
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            createParticles();

            // 埋点：页面加载
            trackEvent('page_loaded', {
                loadTime: performance.now()
            });
        });
    </script>
</body>
</html>
