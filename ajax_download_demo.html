<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AJAX File Download Demo</title>
    <style>
        .progress-container {
            width: 300px;
            margin: 20px auto;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #f3f3f3;
            border-radius: 10px;
            overflow: hidden;
        }
        .progress {
            height: 100%;
            background-color: #4caf50;
            width: 0%;
            transition: width 0.3s ease;
        }
        .stats {
            margin-top: 10px;
            font-family: Arial, sans-serif;
        }
        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="progress-container">
        <div class="progress-bar">
            <div class="progress"></div>
        </div>
        <div class="stats">
            <p>File Size: <span id="fileSize">0 MB</span></p>
            <p>Download Speed: <span id="downloadSpeed">0 KB/s</span></p>
            <p>Progress: <span id="progressPercent">0%</span></p>
        </div>
        <button id="downloadBtn" onclick="startDownload()">Download File</button>
        <button id="cancelBtn" onclick="cancelDownload()" disabled>Cancel Download</button>
    </div>

    <script>
        let xhr = null;

        function startDownload() {
            document.getElementById('downloadBtn').disabled = true;
            document.getElementById('cancelBtn').disabled = false;
            document.getElementById('fileSize').textContent = '0 MB';
            document.getElementById('downloadSpeed').textContent = '0 KB/s';
            document.getElementById('progressPercent').textContent = '0%';
            document.querySelector('.progress').style.width = '0%';

            const url = 'https://lf16-capcut.faceulv.com/obj/capcutpc-packages-sg/installer/capcut_capcutpc_0_1.2.6_installer.exe';
            xhr = new XMLHttpRequest();
            const startTime = Date.now();
            let previousLoaded = 0;

            xhr.open('GET', url, true);
            xhr.responseType = 'blob';

            xhr.onprogress = function(event) {
                if (event.lengthComputable) {
                    const loaded = event.loaded;
                    const total = event.total;
                    const currentTime = Date.now();
                    const timeDiff = (currentTime - startTime) / 1000; // in seconds
                    const speed = ((loaded - previousLoaded) / 1024 / timeDiff).toFixed(2);
                    previousLoaded = loaded;

                    const progress = (loaded / total * 100).toFixed(2);
                    const fileSize = (total / 1024 / 1024).toFixed(2);

                    document.getElementById('fileSize').textContent = `${fileSize} MB`;
                    document.getElementById('downloadSpeed').textContent = `${speed} KB/s`;
                    document.getElementById('progressPercent').textContent = `${progress}%`;
                    document.querySelector('.progress').style.width = `${progress}%`;
                }
            };

            xhr.onload = function() {
                if (this.status === 200) {
                    const blob = new Blob([this.response], { type: 'application/zip' });
                    const link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = 'sample.zip';
                    link.click();
                }
                document.getElementById('downloadBtn').disabled = false;
                document.getElementById('cancelBtn').disabled = true;
            };

            xhr.onerror = function() {
                document.getElementById('downloadSpeed').textContent = 'Error';
                document.getElementById('progressPercent').textContent = 'Error';
                document.getElementById('downloadBtn').disabled = false;
                document.getElementById('cancelBtn').disabled = true;
            };

            xhr.send();
        }

        function cancelDownload() {
            if (xhr) {
                xhr.abort();
                document.getElementById('downloadSpeed').textContent = 'Canceled';
                document.getElementById('progressPercent').textContent = 'Canceled';
                document.querySelector('.progress').style.width = '0%';
                document.getElementById('downloadBtn').disabled = false;
                document.getElementById('cancelBtn').disabled = true;
            }
        }
    </script>
</body>
</html>
