<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EdrawMind - 戦略を描き、資料をつくる | 万兴科技</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@100;300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans JP', sans-serif;
            background: #ffffff;
            color: #000000;
            line-height: 1.6;
        }

        .hero-section {
            background: #ffffff;
            color: #000000;
            padding: 80px 0;
            min-height: 100vh;
            position: relative;
        }

        .brand-logo {
            width: 87px;
            height: 87px;
            background: linear-gradient(135deg, #008899 0%, #66FF66 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 2rem;
            position: relative;
        }

        .brand-logo::before {
            content: '';
            position: absolute;
            width: 31px;
            height: 31px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .brand-logo::after {
            content: '🧠';
            font-size: 2rem;
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-size: 2.25rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            line-height: 1.2;
            color: #000000;
        }

        .hero-description {
            font-size: 0.875rem;
            line-height: 1.71;
            margin-bottom: 3rem;
            color: #000000;
        }

        .features-section {
            background: #ffffff;
            padding: 80px 0;
        }

        .feature-card {
            background: #F5F6F8;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            height: 100%;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(67, 182, 143, 0.2);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .feature-card.purple::before {
            background: rgba(142, 67, 181, 0.2);
        }

        .feature-card:hover::before {
            opacity: 1;
        }

        .feature-title {
            font-size: 1.375rem;
            font-weight: 500;
            margin-bottom: 1rem;
            color: #43B68F;
            line-height: 1.2;
        }

        .feature-title.purple {
            color: #8E43B5;
        }

        .feature-description {
            font-size: 0.75rem;
            color: #000000;
            line-height: 1.42;
            margin-bottom: 1.5rem;
        }

        .feature-image {
            width: 100%;
            height: 200px;
            background: #f0f0f0;
            border-radius: 10px;
            margin-top: 1rem;
            background-size: cover;
            background-position: center;
        }

        .pricing-section {
            background: #ffffff;
            padding: 60px 0;
            text-align: center;
        }

        .pricing-text {
            font-size: 0.875rem;
            font-weight: 100;
            color: #43B68F;
            margin-bottom: 1rem;
            line-height: 1.2;
        }

        .pricing-note {
            font-size: 0.625rem;
            color: #000000;
            line-height: 1.2;
        }

        .cta-button {
            background: #43B68F;
            color: #07273D;
            border: none;
            padding: 20px 50px;
            border-radius: 40px;
            font-weight: 500;
            font-size: 1.375rem;
            transition: all 0.3s ease;
            margin: 40px 0;
            display: inline-flex;
            align-items: center;
            gap: 15px;
        }

        .cta-button:hover {
            background: #3a9d7a;
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(67, 182, 143, 0.3);
            color: #07273D;
        }

        .cta-button::after {
            content: '→';
            font-size: 1.2rem;
        }

        .product-showcase {
            background: #ffffff;
            padding: 60px 0;
        }

        .showcase-image {
            width: 100%;
            max-width: 314px;
            height: 203px;
            background: #f0f0f0;
            border-radius: 15px;
            margin: 0 auto;
            background-size: cover;
            background-position: center;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 1.8rem;
            }

            .feature-title {
                font-size: 1.2rem;
            }

            .cta-button {
                font-size: 1.1rem;
                padding: 15px 35px;
            }

            .brand-logo {
                width: 70px;
                height: 70px;
            }

            .brand-logo::before {
                width: 25px;
                height: 25px;
            }

            .brand-logo::after {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center min-vh-100">
                <div class="col-lg-6">
                    <div class="brand-logo"></div>
                    <h1 class="hero-title">戦略を描き、資料をつくる。</h1>
                    <p class="hero-description">
                        思考を構造化し、戦略や計画を可視化。<br>
                        マインドマップを軸に、情報整理からPowerPoint資料の出力までを一括で対応。<br>
                        業務効率を高める、ビジネス特化型のマインドマップツール。
                    </p>
                </div>
                <div class="col-lg-6">
                    <div class="product-showcase">
                        <div class="showcase-image" style="background-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 314 203%22><rect width=%22314%22 height=%22203%22 fill=%22%23f0f0f0%22/><text x=%2250%25%22 y=%2250%25%22 dominant-baseline=%22middle%22 text-anchor=%22middle%22 fill=%22%23999%22 font-family=%22Arial%22 font-size=%2216%22>EdrawMind Interface</text></svg>');"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-6 mb-4">
                    <div class="feature-card">
                        <h3 class="feature-title">構造化された思考を多角的に可視化</h3>
                        <p class="feature-description">
                            マインドマップに加え、アウトライン・看板・プレゼンテーション表示にも対応。<br>
                            情報を目的別に整理・展開可能。
                        </p>
                        <div class="feature-image" style="background-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 400 200%22><rect width=%22400%22 height=%22200%22 fill=%22%23e8f5f0%22/><circle cx=%22200%22 cy=%22100%22 r=%2230%22 fill=%22%2343B68F%22/><circle cx=%22120%22 cy=%2260%22 r=%2220%22 fill=%22%2366d9a5%22/><circle cx=%22280%22 cy=%2260%22 r=%2220%22 fill=%22%2366d9a5%22/><circle cx=%22120%22 cy=%22140%22 r=%2220%22 fill=%22%2366d9a5%22/><circle cx=%22280%22 cy=%22140%22 r=%2220%22 fill=%22%2366d9a5%22/><line x1=%22200%22 y1=%22100%22 x2=%22120%22 y2=%2260%22 stroke=%22%2343B68F%22 stroke-width=%222%22/><line x1=%22200%22 y1=%22100%22 x2=%22280%22 y2=%2260%22 stroke=%22%2343B68F%22 stroke-width=%222%22/><line x1=%22200%22 y1=%22100%22 x2=%22120%22 y2=%22140%22 stroke=%22%2343B68F%22 stroke-width=%222%22/><line x1=%22200%22 y1=%22100%22 x2=%22280%22 y2=%22140%22 stroke=%22%2343B68F%22 stroke-width=%222%22/></svg>');"></div>
                    </div>
                </div>
                <div class="col-lg-6 mb-4">
                    <div class="feature-card">
                        <h3 class="feature-title">AIによる自動マップ生成</h3>
                        <p class="feature-description">
                            テキストを入力するだけで、論理構造を自動解析。<br>
                            思考の可視化と資料構成を効率化。
                        </p>
                        <div class="feature-image" style="background-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 400 200%22><rect width=%22400%22 height=%22200%22 fill=%22%23f0f8ff%22/><rect x=%2250%22 y=%2250%22 width=%22100%22 height=%2260%22 rx=%225%22 fill=%22%23e0e0e0%22/><rect x=%22250%22 y=%2250%22 width=%22100%22 height=%2260%22 rx=%225%22 fill=%22%2343B68F%22/><text x=%22100%22 y=%2285%22 text-anchor=%22middle%22 fill=%22%23666%22 font-size=%2212%22>Text Input</text><text x=%22300%22 y=%2285%22 text-anchor=%22middle%22 fill=%22white%22 font-size=%2212%22>AI Analysis</text><path d=%22M 150 80 L 250 80%22 stroke=%22%2343B68F%22 stroke-width=%222%22 marker-end=%22url(%23arrowhead)%22/><defs><marker id=%22arrowhead%22 markerWidth=%2210%22 markerHeight=%227%22 refX=%229%22 refY=%223.5%22 orient=%22auto%22><polygon points=%220 0, 10 3.5, 0 7%22 fill=%22%2343B68F%22/></marker></defs></svg>');"></div>
                    </div>
                </div>
                <div class="col-lg-6 mb-4">
                    <div class="feature-card purple">
                        <h3 class="feature-title purple">PowerPoint出力に対応</h3>
                        <p class="feature-description">
                            作成したマップをワンクリックでスライド化。<br>
                            企画書・報告資料を素早く作成。
                        </p>
                        <div class="feature-image" style="background-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 400 200%22><rect width=%22400%22 height=%22200%22 fill=%22%23f8f0ff%22/><rect x=%2250%22 y=%2240%22 width=%22120%22 height=%2280%22 rx=%225%22 fill=%22%238E43B5%22/><rect x=%22230%22 y=%2240%22 width=%22120%22 height=%2280%22 rx=%225%22 fill=%22%23ff6b35%22/><text x=%22110%22 y=%2285%22 text-anchor=%22middle%22 fill=%22white%22 font-size=%2212%22>Mind Map</text><text x=%22290%22 y=%2285%22 text-anchor=%22middle%22 fill=%22white%22 font-size=%2212%22>PowerPoint</text><path d=%22M 170 80 L 230 80%22 stroke=%22%238E43B5%22 stroke-width=%222%22 marker-end=%22url(%23arrowhead2)%22/><defs><marker id=%22arrowhead2%22 markerWidth=%2210%22 markerHeight=%227%22 refX=%229%22 refY=%223.5%22 orient=%22auto%22><polygon points=%220 0, 10 3.5, 0 7%22 fill=%22%238E43B5%22/></marker></defs></svg>');"></div>
                    </div>
                </div>
                <div class="col-lg-6 mb-4">
                    <div class="feature-card purple">
                        <h3 class="feature-title purple">共有とコラボレーション</h3>
                        <p class="feature-description">
                            クラウド上での共同編集・コメント機能により、<br>
                            チーム内の意思統一と情報共有を円滑に。
                        </p>
                        <div class="feature-image" style="background-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 400 200%22><rect width=%22400%22 height=%22200%22 fill=%22%23f8f0ff%22/><circle cx=%22150%22 cy=%22100%22 r=%2230%22 fill=%22%238E43B5%22/><circle cx=%22250%22 cy=%22100%22 r=%2230%22 fill=%22%23ff6b35%22/><circle cx=%22200%22 cy=%2260%22 r=%2220%22 fill=%22%2343B68F%22/><circle cx=%22200%22 cy=%22140%22 r=%2220%22 fill=%22%2343B68F%22/><line x1=%22150%22 y1=%22100%22 x2=%22250%22 y2=%22100%22 stroke=%22%238E43B5%22 stroke-width=%222%22/><line x1=%22200%22 y1=%2260%22 x2=%22200%22 y2=%22140%22 stroke=%22%238E43B5%22 stroke-width=%222%22/><text x=%22200%22 y=%22180%22 text-anchor=%22middle%22 fill=%22%238E43B5%22 font-size=%2212%22>Team Collaboration</text></svg>');"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="pricing-section">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <p class="pricing-text">プランの価格：永続ライセンス¥12,900（税込）から利用可能です。</p>
                    <p class="pricing-note">※5本以上一括ご購入の場合、ボリュームディスカントにて提供可能です。担当者までお問い合わせください。</p>
                    <button class="cta-button">価格・在庫 確認はこちら</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>