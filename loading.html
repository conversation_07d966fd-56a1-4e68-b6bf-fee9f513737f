<!doctype html>
<html lang="en">
<head>

  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <meta name="viewport" content="width=device-width,user-scalable=0,initial-scale=1,maximum-scale=1, minimum-scale=1" />
  <meta name="description" content="loading" />
  <title>loading</title>
  <link rel="canonical" href="https://www.tomoviee.ai/login/loading.html" />
  <link rel="shortcut icon" href="https://www.tomoviee.ai/images/home/<USER>" type="image/x-icon" />
  <style>

  </style>
  <style>
    .loader-container {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 9999;
    }

    .loader {
      width: 6rem;
      height: 6rem;
      border: 3px solid #000;
      border-top-color: transparent;
      /* 黑色圆圈，顶部透明 */
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      from {
        transform: rotate(0deg);
      }

      to {
        transform: rotate(360deg);
      }
    }
  </style>
  <script type="text/javascript">
    var CHANNEL_ID = "1372";
    var SITE_ID = "1086";
    var CMS_LANGUAGE = "en";
    var TEMPLATE_ID = "10015034";
    var PAGE_ID = "540304";
    var TEMPLATE_MODULE = "other";
    var TEMPLATE_TYPE = "other";
  </script>
</head>
<body>

  <div class="loader-container">
    <div class="loader"></div>
  </div>

  <script src="https://neveragain.allstatics.com/2019/assets/vendor/jquery.min.js "></script>
  <script>
    function parseUrlParams(url) {
      const params = {}
      const urlArr = url.split("?")
      if (urlArr.length > 1) {
        const paramsArr = urlArr[1].split("&")
        paramsArr.forEach((item) => {
          const [key, value] = item.split("=")
          params[key] = value
        })
      }
      return params
    }
    // 登录成功后
    if (window.opener) {
      const paramsObj2 = parseUrlParams(window.location.href)
      $.ajax({
        type: "POST",
        url: "https://app.tomoviee.ai/web/v1/login",
        headers: {
          "Content-Type": "application/json; charset=utf-8"
        },
        data: JSON.stringify({
          code: paramsObj2['auth_code']
        }),
        xhrFields: {
          withCredentials: true
        },
        crossDomain: true,
        success: function (res) {
          console.log("API响应新开:", res.data);
          var resultObj = {
            answer: res.data,
            paramsObj: paramsObj2,
            timestamp: new Date().toISOString()
          };
          // 将结果发送回父页面
          window.opener.postMessage({
            type: 'RESPONSE',
            result: resultObj
          });
          window.close();
        },
        error: function (err) {
          console.error("API请求失败:", err);
          // 将错误发送回父页面
          window.parent.postMessage({
            type: 'ERROR',
            error: err.statusText || "请求失败"
          }, event.origin);
        }
      });
    }
    // 监听父页面发来的消息
    window.addEventListener('message', function (event) {
      // 安全检查：验证来源
      // if (event.origin !== "xxx") return;
      var data = event.data;
      // 处理请求消息
      if (data.type === 'REQUEST') {
        console.log("从父页面收到的请求:", data);
        if (data.type === 'REQUEST') {
          console.log("收到父页面请求，开始调用API...");
          const paramsObj = parseUrlParams(window.location.href)
          // 使用 $.ajax 发起请求
          try {
            $.ajax({
              type: "POST",
              url: "https://app.tomoviee.ai/web/v1/login",
              headers: {
                "Content-Type": "application/json; charset=utf-8"
              },
              data: JSON.stringify({
                code: paramsObj['auth_code']
              }),
              xhrFields: {
                withCredentials: true
              },
              crossDomain: true,
              success: function (res) {
                console.log("API响应:", res);
                var resultObj = {
                  answer: res.data,
                  question: data.payload.question,
                  paramsObj: paramsObj,
                  timestamp: new Date().toISOString()
                };
                // 将结果发送回父页面
                window.parent.postMessage({
                  type: 'RESPONSE',
                  requestId: data.requestId,
                  result: resultObj
                }, event.origin);
              },
              error: function (err) {
                console.error("API请求失败:", err);
                // 将错误发送回父页面
                window.parent.postMessage({
                  type: 'ERROR',
                  requestId: data.requestId,
                  error: err.statusText || "请求失败"
                }, event.origin);
              }
            });
          } catch (error) {
            // 发送错误回父页面
            window.parent.postMessage({
              type: 'ERROR',
              requestId: data.requestId,
              error: error.message
            }, event.origin);
          }

        }
      }
    });
  </script>
  <script>
    window.addEventListener('load', function () {
      setTimeout(function () {
        var a = window.performance.getEntriesByType("navigation");
        var formatTime = function (t) {
          return Math.round(1E3 * (t / 1E3 + Number.EPSILON)) / 1E3
        }
        if (a.length > 0) {
          a = a[0]

          var totalTime = a.loadEventEnd - a.startTime;
          var responseLoadTime = a.loadEventEnd - a.requestStart;
          var domLoadTime = a.loadEventStart - a.responseEnd;
          var domTime = a.domComplete - a.responseEnd;

          // 自定义接口上报  http://beta-cmsnew.wondershare.cn/
          fetch('/api/v1/gta/data', {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              'page_id': 540304,
              'totalTime': formatTime(totalTime),
              'responseLoadTime': formatTime(responseLoadTime),
              'domLoadTime': formatTime(domLoadTime),
              'domTime': formatTime(domTime)
            }),
          })
        }
      }, 0)
    })
  </script>

</body>
</html>