<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Network Speed Monitor</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        .speed-container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .speed-value {
            font-size: 3rem;
            font-weight: bold;
            color: #007bff;
            margin: 10px 0;
        }
        .speed-label {
            color: #666;
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <div class="speed-container">
        <div class="speed-value" id="speedValue">0</div>
        <div class="speed-label">Mbps</div>
    </div>

    <script>
        const speedValue = document.getElementById('speedValue');

        function updateNetworkSpeed() {
            if (navigator.connection && navigator.connection.downlink) {
                const speed = navigator.connection.downlink;
                speedValue.textContent = speed.toFixed(2);
            } else {
                speedValue.textContent = 'N/A';
            }
        }

        // Update speed initially and every second
        updateNetworkSpeed();
        setInterval(updateNetworkSpeed, 1000);

        // Listen for network changes
        if (navigator.connection) {
            navigator.connection.addEventListener('change', updateNetworkSpeed);
        }
    </script>
</body>
</html>
