<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Audio API Demo</title>
</head>
<body>
    <h1>Web Audio API Demo</h1>
    <button id="playButton">Play Audio</button>
    <button id="stopButton">Stop Audio</button>

    <script>
        // Create an audio context
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        // Load an audio file
        const audioElement = new Audio('https://www.soundhelix.com/examples/mp3/SoundHelix-Song-2.mp3');
        const track = audioContext.createMediaElementSource(audioElement);
        track.connect(audioContext.destination);

        // Play the audio
        document.getElementById('playButton').addEventListener('click', function() {
            audioElement.play();
        });

        // Stop the audio
        document.getElementById('stopButton').addEventListener('click', function() {
            audioElement.pause();
            audioElement.currentTime = 0;
        });
    </script>
</body>
</html>
